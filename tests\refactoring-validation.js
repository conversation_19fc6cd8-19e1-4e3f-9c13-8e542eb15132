/**
 * Refactoring Validation Test
 * 
 * This script tests the key endpoints to ensure the refactored backend
 * maintains all functionality from the original monolithic structure.
 */

const http = require('http');

const BASE_URL = 'http://localhost:3003';

// Test configuration
const tests = [
  {
    name: 'Health Check - Auth Check',
    method: 'GET',
    path: '/api/auth/check',
    expectedStatus: [200, 401] // Either works, just need server response
  },
  {
    name: 'API Routes - Auth Session Info',
    method: 'GET',
    path: '/api/auth/session',
    expectedStatus: [200, 401]
  },
  {
    name: 'API Routes - Lessons List',
    method: 'GET',
    path: '/api/lessons',
    expectedStatus: [200, 401]
  },
  {
    name: 'API Routes - Ratings Leaderboard',
    method: 'GET',
    path: '/api/ratings/leaderboard',
    expectedStatus: [200, 500] // May fail if no DB connection, but should respond
  },
  {
    name: 'Static Files - Public Assets',
    method: 'GET',
    path: '/favicon.ico',
    expectedStatus: [200, 404] // Either works
  },
  {
    name: '404 Handler - Invalid Route',
    method: 'GET',
    path: '/api/nonexistent',
    expectedStatus: [404]
  }
];

// Test runner
async function runTest(test) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: test.path,
      method: test.method,
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      const success = test.expectedStatus.includes(res.statusCode);
      resolve({
        name: test.name,
        success,
        status: res.statusCode,
        expected: test.expectedStatus,
        message: success ? 'PASS' : `FAIL - Expected ${test.expectedStatus}, got ${res.statusCode}`
      });
    });

    req.on('error', (error) => {
      resolve({
        name: test.name,
        success: false,
        status: 'ERROR',
        expected: test.expectedStatus,
        message: `ERROR - ${error.message}`
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        name: test.name,
        success: false,
        status: 'TIMEOUT',
        expected: test.expectedStatus,
        message: 'TIMEOUT - Request took too long'
      });
    });

    req.end();
  });
}

// Main test function
async function validateRefactoring() {
  console.log('🧪 Starting Refactoring Validation Tests...\n');
  console.log('Testing server at:', BASE_URL);
  console.log('=' .repeat(60));

  const results = [];
  
  for (const test of tests) {
    console.log(`Testing: ${test.name}...`);
    const result = await runTest(test);
    results.push(result);
    
    const icon = result.success ? '✅' : '❌';
    console.log(`${icon} ${result.message}`);
    console.log('');
  }

  // Summary
  console.log('=' .repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(60));
  
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`Total Tests: ${total}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${total - passed}`);
  console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (passed === total) {
    console.log('\n🎉 ALL TESTS PASSED! Refactoring validation successful.');
    console.log('✅ The modular backend is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the server and routes.');
    console.log('❌ Failed tests:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`   - ${r.name}: ${r.message}`);
    });
  }
  
  console.log('\n📝 Next Steps:');
  console.log('1. Run comprehensive functional tests');
  console.log('2. Test authentication flows');
  console.log('3. Validate file upload functionality');
  console.log('4. Test lesson creation and management');
  console.log('5. Verify rating system operations');
}

// Check if server is running first
function checkServerRunning() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3003,
      path: '/',
      method: 'GET',
      timeout: 2000
    }, (res) => {
      resolve(true);
    });

    req.on('error', () => resolve(false));
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Main execution
async function main() {
  console.log('🔍 Checking if server is running...');
  
  const serverRunning = await checkServerRunning();
  
  if (!serverRunning) {
    console.log('❌ Server is not running on port 3003');
    console.log('Please start the server first:');
    console.log('   node api/index.js');
    process.exit(1);
  }
  
  console.log('✅ Server is running\n');
  await validateRefactoring();
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { validateRefactoring, runTest };
